// monitor_catsky.js - Monitor CATSKY token balance
import dotenv from "dotenv";
dotenv.config();

import { getBackendBlockfrostLucidInstance, NetworkId } from "@minswap/sdk";

const BF_PROJECT_ID = process.env.BF_PROJECT_ID;
const MNEMONIC = process.env.MNEMONIC;
const sender = "addr1qx492sqgzk5c7jljapc4kq3jmf29pqz5v39h00dq4wvtszczq5gfxkek2fxdwevtjcjaf8hdap97auc744p8ppjf4vns394f0k";
const CATSKY_UNIT = "9b426921a21f54600711da0be1a12b026703a9bd8eb9848d08c9d921434154534b59";

async function checkBalance() {
  try {
    console.log("🔍 Checking CATSKY balance...");
    
    const lucid = await getBackendBlockfrostLucidInstance(
      NetworkId.MAINNET,
      BF_PROJECT_ID,
      "https://cardano-mainnet.blockfrost.io/api/v0",
      sender
    );
    await lucid.selectWalletFromSeed(MNEMONIC.trim());

    const utxos = await lucid.wallet.getUtxos();
    
    let totalADA = 0n;
    let totalCATSKY = 0n;
    let catskyUtxos = [];

    utxos.forEach((utxo, i) => {
      if (utxo.assets.lovelace) {
        totalADA += utxo.assets.lovelace;
      }
      
      if (utxo.assets[CATSKY_UNIT]) {
        totalCATSKY += utxo.assets[CATSKY_UNIT];
        catskyUtxos.push({
          index: i + 1,
          txHash: utxo.txHash,
          amount: utxo.assets[CATSKY_UNIT].toString()
        });
      }
    });

    console.log("📊 WALLET SUMMARY:");
    console.log("Total ADA:", (Number(totalADA) / 1_000_000).toFixed(6));
    console.log("Total CATSKY:", totalCATSKY.toString());
    
    if (totalCATSKY > 0n) {
      console.log("🎉 SUCCESS: CATSKY tokens found!");
      console.log("CATSKY UTxOs:");
      catskyUtxos.forEach(utxo => {
        console.log(`  UTxO ${utxo.index}: ${utxo.amount} CATSKY (tx: ${utxo.txHash})`);
      });
      return true;
    } else {
      console.log("⏳ No CATSKY tokens yet - order still being processed by batcher");
      return false;
    }
    
  } catch (error) {
    console.error("❌ Error checking balance:", error.message);
    return false;
  }
}

async function monitorWithInterval() {
  console.log("🚀 Starting CATSKY balance monitor...");
  console.log("Expected: ~274,659 CATSKY tokens from recent swap order");
  console.log("Checking every 30 seconds...\n");
  
  let attempts = 0;
  const maxAttempts = 120; // 1 hour of monitoring
  
  const interval = setInterval(async () => {
    attempts++;
    console.log(`\n--- Check ${attempts}/${maxAttempts} (${new Date().toLocaleTimeString()}) ---`);
    
    const hasTokens = await checkBalance();
    
    if (hasTokens) {
      console.log("\n🎉 MONITORING COMPLETE: CATSKY tokens received!");
      clearInterval(interval);
      process.exit(0);
    }
    
    if (attempts >= maxAttempts) {
      console.log("\n⚠️  MONITORING TIMEOUT: Tokens not received after 1 hour");
      console.log("This might indicate:");
      console.log("- Network congestion causing delays");
      console.log("- Order cancelled due to slippage");
      console.log("- Batcher system issues");
      console.log("\nCheck Minswap Discord/Twitter for updates or contact support");
      clearInterval(interval);
      process.exit(1);
    }
    
    console.log(`Next check in 30 seconds... (${maxAttempts - attempts} checks remaining)`);
  }, 30000); // Check every 30 seconds
}

// Run single check if called with 'check' argument
if (process.argv[2] === 'check') {
  checkBalance().then(hasTokens => {
    process.exit(hasTokens ? 0 : 1);
  });
} else {
  // Start continuous monitoring
  monitorWithInterval();
}
