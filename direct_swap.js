//direct_swap.js — DIRECT ADA ➜ CATSKY swap (NO BATCHER!)

import dotenv from "dotenv";
dotenv.config();

import {
  ADA,
  Asset,
  BlockfrostAdapter,
  calculateAmountWithSlippageTolerance,
  Dex,
  DexV2Calculation,
  NetworkId,
  getBackendBlockfrostLucidInstance,
} from "@minswap/sdk";
import { BlockFrostAPI } from "@blockfrost/blockfrost-js";

console.log("🚨 DIRECT SWAP MODE - NO BATCHER ORDERS!");
console.log("BF_PROJECT_ID:", process.env.BF_PROJECT_ID);
console.log("MNEMONIC:", process.env.MNEMONIC ? "Loaded" : "Not loaded");
  
// CONFIG
const BF_PROJECT_ID = process.env.BF_PROJECT_ID;
const MNEMONIC = process.env.MNEMONIC;
const SLIPPAGE_PCT = 2.0; // Higher slippage for direct swaps
const CATSKY = Asset.fromString("9b426921a21f54600711da0be1a12b026703a9bd8eb9848d08c9d921434154534b59");
const AMOUNT_IN = 1_000_000n; // 1 ADA
     
async function main() {
  console.log("🔗 Connecting to Blockfrost...");
  const sender = "addr1qx492sqgzk5c7jljapc4kq3jmf29pqz5v39h00dq4wvtszczq5gfxkek2fxdwevtjcjaf8hdap97auc744p8ppjf4vns394f0k";

  const lucid = await getBackendBlockfrostLucidInstance(
    NetworkId.MAINNET,
    BF_PROJECT_ID,
    "https://cardano-mainnet.blockfrost.io/api/v0",
    sender
  );
  await lucid.selectWalletFromSeed(MNEMONIC.trim());

  console.log("📦 Getting UTxOs...");
  const utxos = await lucid.wallet.getUtxos();
  console.log(`Found ${utxos.length} UTxOs`);
  
  console.log("🧰 Loading adapter...");
  const adapter = new BlockfrostAdapter(NetworkId.MAINNET, new BlockFrostAPI({
    projectId: BF_PROJECT_ID, network: "mainnet"
  }));
    
  console.log("🔎 Searching for V1 pools (DIRECT SWAP)...");
  
  // Try to find V1 pool first (direct swaps)
  let v1Pool = null;
  try {
    const v1Pools = await adapter.getV1Pools({ page: 1, count: 100 });
    console.log(`Found ${v1Pools.length} V1 pools`);
    
    // Look for ADA/CATSKY V1 pool
    v1Pool = v1Pools.find(pool => {
      const hasADA = pool.assetA === "lovelace" || pool.assetB === "lovelace";
      const hasCATSKY = pool.assetA === Asset.toString(CATSKY) || pool.assetB === Asset.toString(CATSKY);
      return hasADA && hasCATSKY;
    });
    
    if (v1Pool) {
      console.log("✅ Found V1 pool for direct swap!");
      console.log("Pool ID:", v1Pool.id);
      console.log("AssetA:", v1Pool.assetA);
      console.log("AssetB:", v1Pool.assetB);
    }
  } catch (e) {
    console.log("⚠️ Could not fetch V1 pools:", e.message);
  }

  if (v1Pool) {
    console.log("🔥 Building DIRECT V1 swap transaction...");
    
    // Calculate expected output
    const isAdaFirst = v1Pool.assetA === "lovelace";
    const reserveIn = isAdaFirst ? BigInt(v1Pool.reserveA) : BigInt(v1Pool.reserveB);
    const reserveOut = isAdaFirst ? BigInt(v1Pool.reserveB) : BigInt(v1Pool.reserveA);
    
    // Use V1 calculation (different from V2)
    const rawOut = (AMOUNT_IN * reserveOut) / (reserveIn + AMOUNT_IN);
    const minimumAmountOut = (rawOut * BigInt(Math.floor((100 - SLIPPAGE_PCT) * 100))) / 10000n;
    
    console.log(`Expected output: ${rawOut} CATSKY (min: ${minimumAmountOut})`);
    
    const dexV1 = new Dex(lucid);
    
    const txComplete = await dexV1.buildSwapExactInTx({
      sender,
      assetIn: ADA,
      amountIn: AMOUNT_IN,
      assetOut: CATSKY,
      minimumAmountOut,
      isLimitOrder: false
    });
    
    console.log("✅ DIRECT swap transaction built!");
    console.log("🖊 Signing & submitting...");
    
    const signedTx = txComplete.sign();
    const completedTx = await signedTx.commit();
    const txHash = await completedTx.submit();
    
    console.log("🎉 DIRECT SWAP SUCCESS! TX hash:", txHash);
    
    // Verify this is a real swap
    console.log("⏳ Waiting 5 seconds then verifying...");
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    try {
      const api = new BlockFrostAPI({ projectId: BF_PROJECT_ID, network: "mainnet" });
      const txDetails = await api.txsUtxos(txHash);
      
      let foundCATSKY = false;
      txDetails.outputs.forEach((output, i) => {
        if (output.address === sender) {
          output.amount.forEach(asset => {
            if (asset.unit === "9b426921a21f54600711da0be1a12b026703a9bd8eb9848d08c9d921434154534b59") {
              console.log(`🎉 CONFIRMED: Received ${asset.quantity} CATSKY tokens!`);
              foundCATSKY = true;
            }
          });
        }
      });
      
      if (!foundCATSKY) {
        console.log("⚠️ No CATSKY found yet - may need more time to confirm");
      }
      
    } catch (e) {
      console.log("Could not verify immediately:", e.message);
    }
    
  } else {
    console.log("❌ No V1 pool found - trying alternative approach...");
    
    // Alternative: Try to build direct transaction using Lucid
    console.log("🔧 Building manual swap transaction...");
    
    // Get V2 pool for reference
    const pool = await adapter.getV2PoolByPair(ADA, CATSKY) ||
                 await adapter.getV2PoolByPair(CATSKY, ADA);
    
    if (!pool) {
      throw new Error("❌ No pools found at all!");
    }
    
    console.log("⚠️ Only V2 pool available - this will create a batcher order");
    console.log("Consider using a different DEX or waiting for V1 pool");
    
    throw new Error("❌ Direct swap not possible - only batcher available");
  }
}

main().catch(err => {
  console.error("❌ DIRECT SWAP FAILED:", err.message);
  console.error("This means:");
  console.error("1. No V1 pools available for direct swaps");
  console.error("2. Only V2 batcher system available");
  console.error("3. Need to find alternative DEX or approach");
  process.exit(1);
});
